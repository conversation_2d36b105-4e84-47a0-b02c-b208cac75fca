package com.cheche365.bc.utils.platform.mapping;


import com.cheche365.bc.utils.platform.MappingRule;
import com.cheche365.bc.utils.platform.PlatformMappingFunction;
import com.cheche365.bc.utils.platform.MappingData;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.cheche365.bc.utils.platform.MappingConstants.MAPPING_DEFAULT_VALUE_KEY;
import static com.cheche365.bc.utils.platform.MappingConstants.MAPPING_KEY;

/**
 *
 * <AUTHOR>
 */
@Slf4j
public class MappingPlatformMappingFunction implements PlatformMappingFunction {

    @Override
    public Object apply(MappingData data, final MappingRule rule) {
        Map<String, Object> extInfo = rule.getExtInfo();
        if (!extInfo.containsKey(MAPPING_KEY)) {
            log.warn("未找到映射的Map");
            return null;
        }
        Map mapping = (Map) extInfo.get(MAPPING_KEY);
        Object defaultValue = extInfo.get(MAPPING_DEFAULT_VALUE_KEY);
        return mapping.getOrDefault(data.getValueFromSource(rule.getKey()), defaultValue);
    }

}
