package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.sharding.QueryTableSwitch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 查询开关Job处理器
 *
 * 参数：
 * - "true": 查询使用旧表
 * - "false": 查询使用新表+HBase
 * - "status": 查看当前状态
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "queryTableSwitchJobHandler")
@AllArgsConstructor
@Slf4j
public class QueryTableSwitchJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            if (StrUtil.isBlank(param)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }

            param = param.trim();

            // 记录当前状态
            boolean beforeStatus = QueryTableSwitch.isUseOldTableForQuery();
            log.info("查询开关当前状态: {}", beforeStatus ? "旧表" : "新表");

            String result = executeSwitch(param, beforeStatus);

            return new ReturnT<>(ReturnT.SUCCESS_CODE, result);

        } catch (Exception e) {
            log.error("查询开关Job执行失败，参数: {}", param, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开关切换
     */
    private String executeSwitch(String param, boolean beforeStatus) {
        if ("status".equalsIgnoreCase(param)) {
            return String.format("查询开关当前状态: %s", beforeStatus ? "旧表" : "新表");
        }

        // 设置新状态
        boolean newStatus = Convert.toBool(param);
        QueryTableSwitch.setUseOldTableForQuery(newStatus);

        // 记录修改后状态
        boolean afterStatus = QueryTableSwitch.isUseOldTableForQuery();
        log.info("查询开关修改后状态: {}", afterStatus ? "旧表" : "新表");

        return String.format("查询开关已从 %s 修改为 %s",
            beforeStatus ? "旧表" : "新表",
            afterStatus ? "旧表" : "新表");
    }
}
