package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 双写开关Job处理器
 *
 * 参数：
 * - "true": 开启双写
 * - "false": 关闭双写
 * - "status": 查看当前状态
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "oldTableSwitchJobHandler")
@AllArgsConstructor
@Slf4j
public class OldTableSwitchJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            if (StrUtil.isBlank(param)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }

            param = param.trim();

            // 记录当前状态
            boolean beforeStatus = OldTableSwitch.isUseOldTable();
            log.info("双写开关当前状态: {}", beforeStatus ? "开启" : "关闭");

            String result = executeSwitch(param, beforeStatus);

            return new ReturnT<>(ReturnT.SUCCESS_CODE, result);

        } catch (Exception e) {
            log.error("双写开关Job执行失败，参数: {}", param, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开关切换
     */
    private String executeSwitch(String param, boolean beforeStatus) {
        if ("status".equalsIgnoreCase(param)) {
            return String.format("双写开关当前状态: %s", beforeStatus ? "开启" : "关闭");
        }

        // 设置新状态
        boolean newStatus = Convert.toBool(param);
        OldTableSwitch.setUseOldTable(newStatus);

        // 记录修改后状态
        boolean afterStatus = OldTableSwitch.isUseOldTable();
        log.info("双写开关修改后状态: {}", afterStatus ? "开启" : "关闭");

        return String.format("双写开关已从 %s 修改为 %s",
            beforeStatus ? "开启" : "关闭",
            afterStatus ? "开启" : "关闭");
    }
}
