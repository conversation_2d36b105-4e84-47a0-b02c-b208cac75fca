package com.cheche365.bc.utils.platform.mapping;

import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.utils.platform.MappingRule;
import com.cheche365.bc.utils.platform.PlatformMappingFunction;
import com.cheche365.bc.utils.platform.MappingData;

import java.util.Map;
import java.util.Objects;

/**
 * 直接从平台字段映射
 *
 * <AUTHOR>
 */
public class CopyFromSourcePlatformMappingFunction implements PlatformMappingFunction {

    @Override
    public Object apply(MappingData data, MappingRule rule) {
        String key = rule.getKey();
        Map source = data.getSource();
        if (!source.containsKey(key)) {
            return "";
        }
        Object value = source.get(key);
        return processValue(value);
    }

    private static Object processValue(Object value) {
        if (Objects.isNull(value)) {
            return "";
        }
        return StrUtil.isNotEmpty(value.toString()) ? value : "";
    }

}
