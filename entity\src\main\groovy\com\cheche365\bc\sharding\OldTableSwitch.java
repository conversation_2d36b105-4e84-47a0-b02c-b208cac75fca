package com.cheche365.bc.sharding;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 双写开关控制类
 * <AUTHOR>
 */
@Slf4j
public class OldTableSwitch {

    private static final AtomicBoolean USE_OLD_TABLE = new AtomicBoolean(true);
    private static final Object initLock = new Object();
    private static volatile boolean initialized = false;

    public static final String CACHE_KEY = "bc:oldTableSwitch";
    private static volatile ScheduledExecutorService scheduler;

    /**
     * 延迟初始化方法
     * 确保在Spring容器完全启动后再初始化
     */
    private static void ensureInitialized() {
        if (!initialized) {
            synchronized (initLock) {
                if (!initialized) {
                    try {
                        init();
                        initialized = true;
                        log.info("OldTableSwitch 初始化完成");
                    } catch (Exception e) {
                        log.warn("OldTableSwitch 初始化失败，将使用默认值: {}", e.getMessage());
                        // 初始化失败时使用默认值，不抛出异常
                    }
                }
            }
        }
    }

    /**
     * 实际的初始化逻辑
     */
    private static void init() {
        boolean value = getOldTableSwitchSafely();
        USE_OLD_TABLE.set(value);
        initScheduler();
    }

    /**
     * 初始化定时任务调度器
     */
    private static void initScheduler() {
        if (scheduler == null) {
            scheduler = Executors.newSingleThreadScheduledExecutor(ThreadFactoryBuilder
                .create()
                .setNamePrefix("OldTableSwitch-scheduler-")
                .setDaemon(true)
                .setUncaughtExceptionHandler((t, e) -> log.error("Failed to execute OldTableSwitch scheduler task", e))
                .build());

            // 注册JVM关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                if (scheduler != null && !scheduler.isShutdown()) {
                    scheduler.shutdown();
                    try {
                        if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                            scheduler.shutdownNow();
                        }
                    } catch (InterruptedException e) {
                        log.error("Failed to shutdown OldTableSwitch scheduler", e);
                    }
                    log.info("OldTableSwitch scheduler stopped");
                }
            }));

            // 启动定时任务
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    boolean newValue = getOldTableSwitchSafely();
                    if (USE_OLD_TABLE.get() != newValue) {
                        USE_OLD_TABLE.set(newValue);
                        log.info("旧表标识已更新为: {}", newValue);
                    }
                } catch (Throwable e) {
                    log.error("Failed to check old table switch", e);
                }
            }, 0, 10, TimeUnit.SECONDS);
        }
    }

    /**
     * 安全地获取旧表开关状态
     * 如果Redis不可用，返回默认值true
     */
    private static boolean getOldTableSwitchSafely() {
        try {
            return getOldTableSwitchFromRedis();
        } catch (Exception e) {
            log.warn("获取旧表标识失败，使用默认值true: {}", e.getMessage());
            return true; // 默认使用旧表，保证安全
        }
    }

    /**
     * 从Redis获取旧表开关状态
     */
    private static boolean getOldTableSwitchFromRedis() throws Exception{
        String value = "";
        try {
            value = RedisUtil.get(CACHE_KEY);
        } catch (Exception e) {
            log.error("获取旧表标识失败", e);
            throw e; // 重新抛出异常，由上层处理
        }

        if (StrUtil.isBlank(value)) {
            try {
                if (RedisUtil.setnx(CACHE_KEY, "true")) {
                    value = "true";
                } else {
                    TimeUnit.MILLISECONDS.sleep(100);
                    value = RedisUtil.get(CACHE_KEY);
                }
            } catch (Exception e) {
                log.error("设置旧表标识失败", e);
                throw e; // 重新抛出异常，由上层处理
            }
        }
        return "true".equals(value);
    }


    /**
     * 获取当前是否使用旧表的标识
     *
     * @return true表示使用旧表，false表示使用分表
     */
    public static boolean isUseOldTable() {
        ensureInitialized(); // 确保已初始化
        return USE_OLD_TABLE.get();
    }

    /**
     * 设置是否使用旧表
     *
     * @param useOldTable true表示使用旧表，false表示使用分表
     */
    public static void setUseOldTable(boolean useOldTable) {
        ensureInitialized(); // 确保已初始化
        USE_OLD_TABLE.set(useOldTable);
        try {
            RedisUtil.set(CACHE_KEY, String.valueOf(useOldTable));
            log.info("旧表开关已设置为: {}", useOldTable);
        } catch (Exception e) {
            log.error("设置旧表开关到Redis失败: {}", e.getMessage());
            // 不抛出异常，避免影响业务流程
        }
    }

}
