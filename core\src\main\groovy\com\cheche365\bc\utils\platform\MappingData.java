package com.cheche365.bc.utils.platform;

import com.cheche365.bc.model.car.CarInfo;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.task.AutoTask;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class MappingData {

    private final AutoTask autoTask;

    private final Map source;

    private final Map<String, Object> target;

    public MappingData(AutoTask autoTask, Map source, Map<String, Object> target) {
        this.autoTask = autoTask;
        this.source = source;
        this.target = target;
    }

    public Map getSource() {
        return source;
    }

    public Object getValueFromSource(String key) {
        return source.get(key);
    }

    public CarInfo getCarInfo() {
        return ((Enquiry) autoTask.getTaskEntity()).getOrder().getCarInfo();
    }

    public Map<String, Object> getTarget() {
        return this.target;
    }

    public AutoTask getAutoTask() {
        return autoTask;
    }
}
