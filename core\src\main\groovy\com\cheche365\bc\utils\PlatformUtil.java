package com.cheche365.bc.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.model.PlatformKey;
import com.cheche365.bc.model.RuleInfoKey;
import com.cheche365.bc.model.car.CarInfo;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.model.car.InsurePerson;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Stream;

/**
 * Created by Co on 2017/3/10.
 */
public class PlatformUtil {

    private static final Logger LOG = LoggerFactory.getLogger(PlatformUtil.class);

    public static final Map<Integer, String> CLAIM_TIMES_MAP = new HashMap<>();

    /**
     * 支持的平台字段集合
     * 字段含义参考 PlatformKey
     */
    private static final Set<String> SUPPORTED_PLATFORM_FIELDS = Set.of(
        PlatformKey.carBrandName,
        PlatformKey.price,
        PlatformKey.taxPrice,
        PlatformKey.analogyPrice,
        PlatformKey.analogyTaxPrice,
        PlatformKey.carModelDate,
        PlatformKey.licenseNo,
        PlatformKey.engineNo,
        PlatformKey.vinNo,
        PlatformKey.seatCnt,
        PlatformKey.modelLoad,
        PlatformKey.fullLoad,
        PlatformKey.displacement,
        PlatformKey.modelCode,
        PlatformKey.vehicleInfo_tradeModelCode,
        PlatformKey.car_specific_selfInsureRate,
        PlatformKey.car_specific_selfChannelRate,
        PlatformKey.car_specific_NcdRate,
        PlatformKey.vehicleInfo_vehicleType,
        PlatformKey.quoteItems_ruleItem_basicRiskPremium,
        PlatformKey.plateform_InsureCo,
        PlatformKey.application_lastInsureCo
    );

    /**
     * 需要从platform复制到def的字段列表
     */
    private static final String[] PLATFORM_DEF_FIELDS = {
        PlatformKey.bizScore,
        RuleInfoKey.BIZ_RATE,
        RuleInfoKey.TRAFFIC_RATE,
        PlatformKey.TRAFFIC_SCORE,
        PlatformKey.SERVICE_CODE,
        RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE,
        RuleInfoKey.geniusItem_channelDiscount,
        RuleInfoKey.geniusItem_insureDiscount,
        RuleInfoKey.geniusItem_totalDiscount,
        PlatformKey.deptChineseName,
        PlatformKey.DEPT_JQ_CHINESE_NAME,
        PlatformKey.application_expectLossRatio,
        PlatformKey.application_expectLossRatioTag
    };

    /**
     * 交强险理赔率与理赔次数描述的映射关系
     */
    private static final Map<Double, String> COMPULSORY_CLAIM_RATE_DESCRIPTION_MAP = new HashMap<>() {{
        put(0.7, "连续三年没有理赔");
        put(0.8, "连续两年没有理赔");
        put(0.9, "上年没有理赔");
        put(1.0, "新保或上年发生一次有责任不涉及死亡理赔");
        put(1.1, "上年有两次及以上理赔");
        put(1.3, "上年有涉及死亡理赔");
    }};

    static {
        CLAIM_TIMES_MAP.put(0, "连续承保期间没有出险");
        CLAIM_TIMES_MAP.put(1, "连续承保期间出险一次");
        CLAIM_TIMES_MAP.put(2, "连续承保期间出险两次");
        CLAIM_TIMES_MAP.put(3, "连续承保期间出险三次");
        CLAIM_TIMES_MAP.put(4, "连续承保期间出险四次");
        CLAIM_TIMES_MAP.put(5, "连续承保期间出险五次");
        CLAIM_TIMES_MAP.put(6, "连续承保期间出险六次");
        CLAIM_TIMES_MAP.put(7, "连续承保期间出险七次");
        CLAIM_TIMES_MAP.put(8, "连续承保期间出险八次");
        CLAIM_TIMES_MAP.put(9, "连续承保期间出险九次");
        CLAIM_TIMES_MAP.put(10, "连续承保期间出险十次");
        CLAIM_TIMES_MAP.put(11, "连续承保期间出险十次以上");
    }

    /**
     * 获取各保险公司开发返回的平台信息
     *
     * @param autoTask
     */
    public static void initPlatformInfo(AutoTask autoTask) {

        if (!autoTask.getTempValues().containsKey(PlatformKey.platformBack)) {
            LOG.info("TempValues未获取到平台信息，请确认是否返回平台信息");
            return;
        }

        try {
            //获取中间变量中各开发存储的平台信息.
            Object platformInfo = autoTask.getTempValues().get(PlatformKey.platformBack);
            if (platformInfo == null) {
                LOG.error("平台信息为空");
                return;
            }

            final Map temp = (Map) platformInfo;

            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
            initializePlatformInfoData(enquiry);
            Map<String, Object> platform = enquiry.getOrder().getPlatformInfo();

            final Map<String, Object> task = processPlatformFields(temp, enquiry);
            final Map<String, String> def = new HashMap<>(4);

            platform.putAll(temp);
            processDefData(platform, def);
            platform.put("definition", def);
            platform.put("task", task);
            platform.put("taskId", autoTask.getTraceKey());
            platform.put("monitorid", autoTask.getTempValues().get("monitorid"));
            //需求 4048 回写平台信息的firstInsureType字段，公共方法逻辑的变更 BY WangJie
            fillFirstInsureType(temp, enquiry, platform);

            fillCommercialClaimTimes(temp, platform);
            //上年交强险理赔次数和交强险浮动原因
            processCompulsoryClaimRate(temp, platform);
            if (!task.isEmpty() && !platform.isEmpty()) {
                platform.put(PlatformKey.succeed, "success");
            }

            LOG.info("生成的平台信息为  {}", new JSONObject(platform).toJSONString());
        } catch (Exception e) {
            LOG.error("任务：{}，初始化平台信息参数出错：{}", autoTask.getTempValues().get("enquiryId"), ExceptionUtils.getStackTrace(e));
        }
    }

    private static void processCompulsoryClaimRate(Map temp, Map<String, Object> platform) {
        if (temp.containsKey(PlatformKey.compulsoryClaimRate)) {
            double compulsoryClaimRate = 0.0;
            if (StringUtil.isNoEmpty(temp.get(PlatformKey.compulsoryClaimRate).toString())) {
                compulsoryClaimRate = Double.valueOf(temp.get(PlatformKey.compulsoryClaimRate).toString());
            }
            String compulsoryClaimRateReasons = getBwCompulsoryClaimTimes(compulsoryClaimRate);
            if (StringUtil.isNoEmpty(compulsoryClaimRateReasons)) {
                platform.put(PlatformKey.compulsoryClaimRateReasons, compulsoryClaimRateReasons);
            }
        }
    }


    /**
     * 处理平台数据到def映射的转换
     * 优化后的版本：使用数据驱动方式消除重复代码
     *
     * @param platform 平台数据源
     * @param def      目标映射
     */
    private static void processDefData(Map<String, Object> platform, Map<String, String> def) {
        if (platform == null || def == null) {
            return;
        }
        // 批量处理字段映射
        for (String fieldKey : PLATFORM_DEF_FIELDS) {
            processFieldValue(platform, def, fieldKey);
        }
        Object definition = platform.get("definition");
        if (definition != null && definition instanceof Map) {
            def.putAll((Map) definition);
        }
    }

    /**
     * 处理单个字段值的提取和设置
     *
     * @param platform 源数据
     * @param def      目标映射
     * @param fieldKey 字段键
     */
    private static void processFieldValue(Map<String, Object> platform, Map<String, String> def, String fieldKey) {
        try {
            final String value = RuleUtil.checkValue(platform, fieldKey);
            if (StringUtil.isNoEmpty(value)) {
                def.put(fieldKey, value);
            }
        } catch (Exception e) {
            LOG.warn("处理字段 {} 时发生异常: {}", fieldKey, e.getMessage());
        }
    }

    /**
     * 初始化平台信息对象
     *
     * @param enquiry
     */
    private static void initializePlatformInfoData(Enquiry enquiry) {
        if (enquiry.getOrder().getPlatformInfo() == null) {
            enquiry.getOrder().setPlatformInfo(new HashMap<>(40));
        }
    }

    private static String checkNew(Date firstRegDate) throws Exception {
        LocalDate regDate = LocalDateTime.ofInstant(firstRegDate.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();
        long monthDiff = ChronoUnit.MONTHS.between(regDate, now);
        if (monthDiff > 9) {
            return "旧车首次投保";
        } else {
            return "新车首次投保";
        }
    }


    /**
     * 返回平台信息给cm
     *
     * @param autoTask
     * @return
     */
    public static boolean doBackPlatformInfo(AutoTask autoTask) {
        return doBackPlatformInfo(autoTask, false);
    }


    public static boolean doBackPlatformInfo(AutoTask autoTask, boolean notDoInitMethod) {

        if (StringUtils.isBlank(autoTask.getPlatFormSaveUrl())) {
            return false;
        }
        try {
            if (!notDoInitMethod) {
                initPlatformInfo(autoTask);
            }
            String url = autoTask.getPlatFormSaveUrl();
            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
            if (Objects.nonNull(enquiry) && Objects.nonNull(enquiry.getOrder().getPlatformInfo())) {
                LOG.info("回写平台信息报文为：{}", JSONObject.toJSONString(enquiry.getOrder().getPlatformInfo()));
                String result = HttpSender.doPost(url, JSONObject.toJSONString(enquiry.getOrder().getPlatformInfo()));
                autoTask.getTempValues().put("isReservedRes", result);
                return true;
            }
        } catch (Exception e) {
            LOG.error("回写平台信息异常: {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * 上年商业险理赔次数,需求2962
     *
     * @param claimTimes
     * @return
     */

    private static String getClaimTimes(int claimTimes) {
        return CLAIM_TIMES_MAP.get(claimTimes);
    }


    /**
     * 上年交强险理赔次数和交强险浮动原因
     *
     * @param compulsoryClaimRate
     * @return
     */
    private static String getBwCompulsoryClaimTimes(double compulsoryClaimRate) {
        return COMPULSORY_CLAIM_RATE_DESCRIPTION_MAP.getOrDefault(compulsoryClaimRate, "未匹配到理赔次数");
    }

    //设置 重复投保日期
    public static String setRepeatDate(String bizDate, String trDate) {
        StringBuffer repeatDate = new StringBuffer("");
        if (StringUtil.isNoEmpty(bizDate) && StringUtil.isNoEmpty(trDate)) {
            repeatDate.append("商业险终保日期：" + bizDate);
            repeatDate.append(" 交强险终保日期：" + trDate);

        } else {
            if (StringUtil.isNoEmpty(bizDate)) {
                repeatDate.append("商业险终保日期：" + bizDate);

            }
            if (StringUtil.isNoEmpty(trDate)) {
                repeatDate.append("交强险终保日期：" + trDate);
            }

        }
        return repeatDate.toString();
    }


    public static void callBackErrorMsg(AutoTask autoTask) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorcode", autoTask.getErrorInfo().get("errorcode"));
            jsonObject.put("errordesc", autoTask.getErrorInfo().get("errordesc"));
            jsonObject.put("taskId", autoTask.getTraceKey());
            String result = HttpSender.doPost(autoTask.getPlatFormSaveUrl() != null ? autoTask.getPlatFormSaveUrl() : "", jsonObject.toJSONString());
            autoTask.getTempValues().put("isReservedRes", result);
            LOG.info("重复投保回写平台信息报文为：" + jsonObject + "\nresult:" + result);
        } catch (Exception e) {
            LOG.info("重复投保平台信息回写失败");
        }
    }

    public static boolean doBackPlatformInfo(AutoTask autoTask, Map<String, Object> enquiry, Map<String, Object> tempValues) {
        Enquiry transform = TaskUtil.transform(JSON.toJSONString(enquiry), autoTask.getTaskType());
        transform.getOrder().setPlatformInfo((Map<String, Object>) tempValues.get("platformInfo"));
        transform.getOrder().setInsurePerson(JSON.parseObject(enquiry.get("insurePerson").toString(), InsurePerson.class));
        String insuredPersonListStr = enquiry.get("insuredPersonList").toString();
        List<InsurePerson> insuredPersonList = JSON.parseArray(insuredPersonListStr, InsurePerson.class);
        transform.getOrder().setInsuredPersons(insuredPersonList);
        autoTask.setTaskEntity(transform);
        autoTask.setTempValues(tempValues);

        boolean flag = doBackPlatformInfo(autoTask, false);
        tempValues.put("platformInfo", ((Enquiry) autoTask.getTaskEntity()).getOrder().getPlatformInfo());

        Map<String, Object> taskEntity = new HashMap<>();
        taskEntity.put("enquiry", enquiry);
        autoTask.setTaskEntity(taskEntity);
        return flag;
    }

    @SuppressWarnings("unchecked")
    private static void fillCommercialClaimTimes(Map platformInfos, Map platfromInfoMap) {
        Stream.of(RuleInfoKey.application_commercialClaimTimes, PlatformKey.bwCommercialClaimTimes)
            .map((String key) -> String.valueOf(platformInfos.getOrDefault(key, "")))
            .filter(StrUtil::isNotBlank)
            .findFirst()
            .ifPresent((String claimTimes) -> {
                if (NumberUtil.isNumber(claimTimes)) {
                    try {
                        int iCommercialClaimTimesClaimTimes = Double.valueOf(claimTimes).intValue();
                        String result = getClaimTimes(iCommercialClaimTimesClaimTimes);
                        platfromInfoMap.put(PlatformKey.bwCommercialClaimTimes, result);
                    } catch (Exception e) {
                        LOG.error("连续承保期间出险次数抓取非整数: {}", claimTimes);
                    }
                } else {
                    LOG.error("无法解析商业险理赔次数: {}", claimTimes);
                }
            });
    }


    /**
     * 处理平台字段映射
     *
     * @param temp 原始平台数据
     * @return 处理后的任务数据
     */
    private static Map<String, Object> processPlatformFields(Map temp, Enquiry enquiry) {
        final Map<String, Object> task = new HashMap<>(SUPPORTED_PLATFORM_FIELDS.size());

        temp.forEach((k, v) -> {
            String key = (String) k;
            if (SUPPORTED_PLATFORM_FIELDS.contains(key)) {
                task.put(key, processFieldValue(v));
            }
        });

        CarInfo carInfo = enquiry.getOrder().getCarInfo();
        //非续保
        if (!enquiry.isRenewal()) {
            //从数据源获取车俩基本信息
            task.put(PlatformKey.licenseNo, carInfo.getPlateNum());
            task.put(PlatformKey.engineNo, carInfo.getEngineNum());
            task.put(PlatformKey.vinNo, carInfo.getVin());
            task.put(PlatformKey.seatCnt, carInfo.getSeatCnt());
            task.put(PlatformKey.modelLoad, carInfo.getModelLoad());
        }
        return task;
    }

    /**
     * 处理字段值，统一处理逻辑
     *
     * @param value 原始值
     * @return 处理后的值
     */
    private static Object processFieldValue(Object value) {
        if (Objects.isNull(value)) {
            return "";
        }
        return StringUtil.isNoEmpty(value.toString()) ? value : "";
    }

    /**
     * 设置投保类型字段
     * 优化后的方法：提高代码可读性和健壮性
     *
     * @param temp     临时数据
     * @param enquiry  询价对象
     * @param platform 平台信息
     */
    private static void fillFirstInsureType(Map<String, Object> temp, Enquiry enquiry, Map<String, Object> platform) {
        try {
            // 优先使用商业险连续承保年数判断
            String firstInsureType = determineFirstInsureTypeByBizYears(temp, enquiry);

            // 如果无法通过承保年数判断，则使用NCD系数作为备用方案
            if (firstInsureType == null) {
                firstInsureType = determineFirstInsureTypeByNcd(temp);
            }

            // 设置投保类型
            if (firstInsureType != null) {
                platform.put(PlatformKey.firstInsureType, firstInsureType);
            }
        } catch (Exception e) {
            LOG.error("设置投保类型时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据商业险连续承保年数判断投保类型
     *
     * @param temp    临时数据
     * @param enquiry 询价对象
     * @return 投保类型，如果无法判断则返回null
     */
    private static String determineFirstInsureTypeByBizYears(Map<String, Object> temp, Enquiry enquiry) {
        Object bizYearsObj = temp.get(PlatformKey.bizContinuityInsureYears);

        // 检查商业险连续承保年数是否存在且有效
        if (StrUtil.isEmptyIfStr(bizYearsObj) || !NumberUtil.isNumber(bizYearsObj.toString())) {
            return null;
        }

        try {
            double bizYears = Double.parseDouble(bizYearsObj.toString());

            if (bizYears == 0) {
                // 承保年数为0，根据车辆初登日期判断新车还是旧车首次投保
                CarInfo carInfo = enquiry.getOrder().getCarInfo();
                if (carInfo != null && carInfo.getFirstRegDate() != null) {
                    return checkNew(carInfo.getFirstRegDate());
                }
                return "首次投保"; // 默认值
            } else {
                // 承保年数大于0，非首次投保
                return "非首次投保";
            }
        } catch (NumberFormatException e) {
            LOG.warn("商业险连续承保年数格式错误: {}", bizYearsObj);
            return null;
        } catch (Exception e) {
            LOG.warn("根据承保年数判断投保类型时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据无赔款优待系数(NCD)判断投保类型
     *
     * @param temp 临时数据
     * @return 投保类型，如果无法判断则返回null
     */
    private static String determineFirstInsureTypeByNcd(Map<String, Object> temp) {
        Object ncdObj = temp.get(PlatformKey.noClaimDiscountCoefficient);

        // 检查NCD系数是否存在且有效
        if (Objects.isNull(ncdObj) || !NumberUtil.isNumber(ncdObj.toString())) {
            return null;
        }

        try {
            double ncd = Double.parseDouble(ncdObj.toString());

            // NCD系数不等于1.0说明有历史投保记录
            if (ncd != 1.0) {
                return "非首次投保";
            }

            // NCD系数等于1.0无法确定是否首次投保，返回null
            return null;
        } catch (NumberFormatException e) {
            LOG.warn("无赔款优待系数格式错误: {}", ncdObj);
            return null;
        }
    }

}
