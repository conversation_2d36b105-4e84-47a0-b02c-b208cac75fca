package com.cheche365.bc.utils.platform;

import com.cheche365.bc.utils.platform.mapping.CopyFromSourcePlatformMappingFunction;
import com.cheche365.bc.utils.platform.mapping.MappingPlatformMappingFunction;

/**
 *
 * <AUTHOR>
 */
public enum MappingFunctionType {
    /**
     * 从平台字段映射到目标字段
     */
    COPY_FROM_SOURCE(CopyFromSourcePlatformMappingFunction.class),
    MAPPING(MappingPlatformMappingFunction.class);

    private final Class<? extends PlatformMappingFunction> mappingFunctionClass;

    MappingFunctionType(Class<? extends PlatformMappingFunction> mappingFunctionClass) {
        this.mappingFunctionClass = mappingFunctionClass;
    }

}
