package com.cheche365.bc.sharding;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 开关初始化器
 * 在Spring容器启动完成后主动初始化开关类
 * 确保Redis连接可用时再进行初始化
 *
 * <AUTHOR>
 */
@Component
@Order(1000) // 较低优先级，确保Redis等基础设施已初始化
@Slf4j
public class SwitchInitializer implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化表切换开关...");

        try {
            // 主动触发初始化
            initializeSwitches();
            log.info("表切换开关初始化完成");
        } catch (Exception e) {
            log.warn("表切换开关初始化失败，将在首次使用时重试: {}", e.getMessage());
        }
    }

    /**
     * 初始化所有开关
     */
    private void initializeSwitches() {
        log.info("=== 表切换开关已移除 ===");
        log.info("当前使用单一存储模式：分片表 + HBase");
        log.info("========================");
    }
}
