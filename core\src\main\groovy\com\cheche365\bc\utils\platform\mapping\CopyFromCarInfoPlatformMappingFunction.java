package com.cheche365.bc.utils.platform.mapping;

import com.cheche365.bc.model.car.CarInfo;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.utils.platform.MappingData;
import com.cheche365.bc.utils.platform.MappingRule;
import com.cheche365.bc.utils.platform.PlatformMappingFunction;

/**
 *
 * <AUTHOR>
 */
public class CopyFromCarInfoPlatformMappingFunction implements PlatformMappingFunction {
    @Override
    public Object apply(MappingData data, MappingRule rule) {
        Enquiry enquiry = (Enquiry) data.getAutoTask().getTaskEntity();

        return null;
    }
}
